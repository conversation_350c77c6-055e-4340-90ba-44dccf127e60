// WebSocket 測試客戶端 (互動式版本)
const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const fetch = require('node-fetch');
const readline = require('readline');
const fs = require('fs');
const path = require('path');
const { buildWebSocketUrl } = require('../utils/networkUtils');

// 用於保存每個裝置的 imageCode
const deviceImageCodes = {};

// Rawdata 格式常數
const RAWDATA_FORMATS = {
  RAWDATA: 'rawdata',
  RUNLENDATA: 'runlendata'
};

// Run-Length 解壓縮函數（與 Go 版本對應）
function decompressRunLength(compressedData) {
  const decompressed = [];
  let i = 0;

  while (i < compressedData.length) {
    const header = compressedData[i];
    i++;

    if ((header & 0x80) === 0) {
      // 重複序列：bit7 = 0
      const runLength = header;
      if (i >= compressedData.length) {
        throw new Error('Incomplete RLE data: missing value byte');
      }
      const value = compressedData[i];
      i++;

      for (let j = 0; j < runLength; j++) {
        decompressed.push(value);
      }
    } else {
      // 非重複序列：bit7 = 1
      const length = header & 0x7F;
      if (i + length > compressedData.length) {
        throw new Error('Incomplete RLE data: insufficient data bytes');
      }

      for (let j = 0; j < length; j++) {
        decompressed.push(compressedData[i + j]);
      }
      i += length;
    }
  }

  return new Uint8Array(decompressed);
}

// 通用解壓縮函數
function decompressRawdata(rawdata, format) {
  switch (format) {
    case RAWDATA_FORMATS.RAWDATA:
      return rawdata; // 無需解壓縮
    case RAWDATA_FORMATS.RUNLENDATA:
      // RLE 壓縮數據，需要正確處理 ImageInfo 和像素數據
      const rawdataBytes = rawdata instanceof Uint8Array ? rawdata : new Uint8Array(rawdata);

      if (rawdataBytes.length < 12) {
        throw new Error('Rawdata too small to contain ImageInfo (12 bytes)');
      }

      // 分離 ImageInfo (前 12 bytes) 和壓縮的像素數據
      const imageInfo = rawdataBytes.slice(0, 12);
      const compressedPixels = rawdataBytes.slice(12);

      // 解壓縮像素數據
      const decompressedPixels = decompressRunLength(compressedPixels);

      // 重新組合完整數據
      const completeData = new Uint8Array(12 + decompressedPixels.length);
      completeData.set(imageInfo, 0);
      completeData.set(decompressedPixels, 12);

      return completeData;
    default:
      console.warn(`未知的 rawdata 格式: ${format}，當作未壓縮處理`);
      return rawdata;
  }
}

// 配置
const API_BASE_URL = 'http://localhost:3001/api';
let TOKEN = '';
let GATEWAY_TOKEN = '';
const jwtSecret = 'your_jwt_secret_here'; // 與服務器端設置一致
//const jwtSecret = 'your-super-secret-jwt-key-change-this-in-production'; // 與服務器端設置一致


// 分片接收器類別
class ChunkReceiver {
  constructor(ws) {
    this.ws = ws;
    this.receivedChunks = new Map();    // 追蹤已接收的分片
    this.chunkBuffer = new Map();       // 儲存分片資料
    this.currentChunkId = null;         // 當前傳輸的 chunkId
    this.expectedTotalChunks = 0;       // 預期總分片數
    this.duplicateCount = 0;            // 重複分片計數
    this.receivedCount = 0;             // 已接收分片計數
    this.deviceMac = null;              // 設備MAC地址
    this.imageCode = null;              // 圖片代碼
  }

  // 處理分片開始訊息
  async handleChunkStart(message) {
    this.cleanup();

    this.currentChunkId = message.chunkId;
    this.expectedTotalChunks = message.totalChunks;
    this.deviceMac = message.deviceMac;
    this.imageCode = message.imageCode;
    this.dataType = message.dataType || 'rawdata'; // 添加數據格式追蹤

    console.log(`📦 開始接收分片: ${message.chunkId}, 總分片數: ${message.totalChunks}, 設備: ${this.deviceMac}, 格式: ${this.dataType}`);

    // 發送開始確認
    this.sendStartAck(message.chunkId, 'ready');
  }

  // 處理二進制分片數據（嵌入式Index）
  async handleBinaryChunkData(binaryData) {
    if (binaryData.length < 4) {
      console.error('❌ 收到的分片數據太小，無法包含index');
      return;
    }

    // 解析前4bytes獲得chunkIndex
    const indexView = new DataView(binaryData.buffer, binaryData.byteOffset, 4);
    const chunkIndex = indexView.getUint32(0, true); // little-endian

    // 提取實際數據（跳過前4bytes的index）
    const actualData = new Uint8Array(binaryData.buffer, binaryData.byteOffset + 4, binaryData.length - 4);

    console.log(`📥 收到分片 ${chunkIndex}: ${actualData.length} bytes 數據 + 4 bytes index`);

    // 重複檢測
    if (this.receivedChunks.has(chunkIndex)) {
      console.log(`🔄 檢測到重複分片: ${chunkIndex}`);
      this.duplicateCount++;
      this.sendAck(this.currentChunkId, chunkIndex, 'duplicate');
      return;
    }

    // 處理新分片
    try {
      // 儲存分片資料到緩衝區
      this.chunkBuffer.set(chunkIndex, new Uint8Array(actualData));

      // 記錄接收狀態
      this.receivedChunks.set(chunkIndex, {
        received: true,
        timestamp: new Date().toISOString(),
        size: actualData.length
      });

      this.receivedCount++;
      console.log(`✅ 分片 ${chunkIndex} 已儲存，進度: ${this.receivedCount}/${this.expectedTotalChunks}`);

      // 回應成功確認
      this.sendAck(this.currentChunkId, chunkIndex, 'received');

      // 檢查是否接收完成
      if (this.receivedCount === this.expectedTotalChunks) {
        console.log(`🎯 所有分片接收完成，開始重組圖片`);
        await this.reassembleImage();
      }

    } catch (error) {
      console.error(`❌ 處理分片 ${chunkIndex} 時出錯:`, error);
      this.sendAck(this.currentChunkId, chunkIndex, 'error', error.message);
    }
  }

  // 處理分片完成訊息
  async handleChunkComplete(message) {
    console.log(`📊 分片傳輸統計: ${this.receivedCount} 接收, ${this.duplicateCount} 重複`);

    // 發送完成確認
    this.sendCompleteAck(message.chunkId, 'success');
  }

  // 重組完整圖片
  async reassembleImage() {
    try {
      // 按順序重組分片
      const totalSize = Array.from(this.chunkBuffer.values())
        .reduce((sum, chunk) => sum + chunk.length, 0);

      const completeData = new Uint8Array(totalSize);
      let offset = 0;

      for (let i = 0; i < this.expectedTotalChunks; i++) {
        const chunkData = this.chunkBuffer.get(i);
        if (!chunkData) {
          throw new Error(`缺少分片: ${i}`);
        }

        completeData.set(chunkData, offset);
        offset += chunkData.length;
      }

      console.log(`🎯 圖片重組完成: ${totalSize} bytes`);

      // 保存重組後的圖片
      await this.saveReassembledData(completeData, this.deviceMac, this.imageCode, this.dataType);

    } catch (error) {
      console.error(`❌ 重組失敗:`, error);
    }
  }

  // 發送各種ACK回應
  sendStartAck(chunkId, status, error = null) {
    const ackMessage = {
      type: 'chunk_start_ack',
      chunkId: chunkId,
      status: status,
      error: error,
      timestamp: new Date().toISOString()
    };
    console.log(`📤 發送開始ACK: ${status}`);
    this.ws.send(JSON.stringify(ackMessage));
  }

  sendAck(chunkId, chunkIndex, status, error = null) {
    const ackMessage = {
      type: 'chunk_ack',
      chunkId: chunkId,
      chunkIndex: chunkIndex,
      status: status,
      error: error,
      timestamp: new Date().toISOString(),
      receivedCount: this.receivedCount,
      duplicateCount: this.duplicateCount
    };
    console.log(`📤 發送分片ACK: chunk ${chunkIndex}, status: ${status}`);
    this.ws.send(JSON.stringify(ackMessage));
  }

  sendCompleteAck(chunkId, status, error = null) {
    const ackMessage = {
      type: 'chunk_complete_ack',
      chunkId: chunkId,
      status: status,
      receivedSize: Array.from(this.chunkBuffer.values()).reduce((sum, chunk) => sum + chunk.length, 0),
      error: error,
      timestamp: new Date().toISOString()
    };
    console.log(`📤 發送完成ACK: ${status}`);
    this.ws.send(JSON.stringify(ackMessage));
  }

  // 保存重組後的數據
  async saveReassembledData(rawBuffer, deviceMac, imageCode, format = 'rawdata') {
    try {
      console.log(`準備保存重組後的 ${format} 格式數據為 bin 檔案，裝置 MAC: ${deviceMac}`);

      // 檢查 buffer 是否有效
      if (!Buffer.isBuffer(rawBuffer) && !(rawBuffer instanceof Uint8Array)) {
        console.warn('無效的原始數據 buffer，跳過保存');
        return;
      }

      // 確保是 Buffer 格式
      const finalBuffer = Buffer.isBuffer(rawBuffer) ? rawBuffer : Buffer.from(rawBuffer);

      if (finalBuffer.length === 0) {
        console.warn('原始數據 buffer 為空，跳過保存');
        return;
      }

      // 建立保存原始數據的目錄
      const path = require('path');
      const fs = require('fs');
      const saveDir = path.join(__dirname, 'saved_images');
      if (!fs.existsSync(saveDir)) {
        fs.mkdirSync(saveDir, { recursive: true });
      }

      // 根據格式調整檔案名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${format}_chunked_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
      const filePath = path.join(saveDir, fileName);

      // 寫入文件
      fs.writeFileSync(filePath, finalBuffer);

      console.log(`已成功將重組後的 ${format} 數據保存到: ${filePath}`);
      console.log(`數據大小: ${finalBuffer.length} 字節`);

      // 如果是壓縮格式，嘗試解壓縮驗證
      if (format !== 'rawdata') {
        try {
          const decompressed = decompressRawdata(finalBuffer, format);
          console.log(`解壓縮驗證成功，解壓縮後大小: ${decompressed.length} 字節`);

          if (finalBuffer.length < decompressed.length) {
            const compressionRatio = (finalBuffer.length / decompressed.length * 100).toFixed(1);
            console.log(`壓縮比: ${compressionRatio}%`);
          }

          // 保存解壓縮後的數據
          const decompressedFileName = `rawdata_decompressed_chunked_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
          const decompressedFilePath = path.join(saveDir, decompressedFileName);
          fs.writeFileSync(decompressedFilePath, Buffer.from(decompressed));
          console.log(`解壓縮數據已保存到: ${decompressedFilePath}`);
        } catch (decompressError) {
          console.error('解壓縮驗證失敗:', decompressError.message);
        }
      }

      // 如果提供了 imageCode，記錄關聯
      if (imageCode) {
        console.log(`關聯的 imageCode: ${imageCode}`);

        // 保存到裝置 imageCode 集合中
        deviceImageCodes[deviceMac] = imageCode;
        console.log(`已將 imageCode ${imageCode} 儲存至本地變數，將在下次發送裝置狀態時使用`);
      }

      // 顯示原始數據的前幾個字節（用於調試）
      const previewBytes = finalBuffer.subarray(0, Math.min(16, finalBuffer.length));
      console.log(`重組數據前 ${previewBytes.length} 字節 (hex): ${Buffer.from(previewBytes).toString('hex')}`);

    } catch (error) {
      console.error('保存重組後的原始數據失敗:', error);
    }
  }

  // 清理資源
  cleanup() {
    this.receivedChunks.clear();
    this.chunkBuffer.clear();
    this.duplicateCount = 0;
    this.receivedCount = 0;
    this.currentChunkId = null;
    this.expectedTotalChunks = 0;
    this.deviceMac = null;
    this.imageCode = null;
    this.dataType = null;
  }
}

// 建立命令行界面
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 提示用戶輸入
const prompt = (query) => new Promise((resolve) => rl.question(query, resolve));

// 登入函數
async function login(username, password) {
  try {
    console.log(`登入中，用戶名: ${username}...`);
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ username, password })
    });

    if (!response.ok) {
      throw new Error(`登入失敗: ${response.status}`);
    }

    // 嘗試從響應中獲取 token
    const data = await response.json();

    // 如果 headers 中沒有 token，嘗試從 response body 獲取
    if (data.token) {
      TOKEN = data.token;
      console.log('登入成功，已從回應中獲取 API token');
      console.log('同時獲得 WebSocket token');
      return true;
    }

    throw new Error('無法從回應中獲取 token');
  } catch (error) {
    console.error('登入錯誤:', error.message);
    return false;
  }
}

// 獲取Gateway token函數
async function getGatewayToken(gatewayId, storeId, macAddress) {
  try {
    console.log(`獲取Gateway token: ${gatewayId}@${storeId}...`);
    const response = await fetch(`${API_BASE_URL}/auth/gateway-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': `token=${TOKEN}`
      },
      credentials: 'include',
      body: JSON.stringify({
        gatewayId,
        storeId,
        macAddress
      })
    });

    if (!response.ok) {
      throw new Error(`獲取Gateway token失敗: ${response.status}`);
    }

    const data = await response.json();

    if (data.token) {
      GATEWAY_TOKEN = data.token;
      console.log('✅ Gateway token獲取成功');
      console.log(`🔗 WebSocket URL: ${data.wsUrl}`);
      console.log(`⏰ 過期時間: ${data.expiresIn}秒`);
      return data;
    }

    throw new Error('無法從回應中獲取Gateway token');
  } catch (error) {
    console.error('獲取Gateway token錯誤:', error.message);
    return null;
  }
}

// 通用請求函數
async function makeRequest(endpoint, method = 'GET', data = null) {
  const url = `${API_BASE_URL}/${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': `token=${TOKEN}`
    },
    credentials: 'include'
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);

    // 檢查響應狀態
    if (!response.ok) {
      let errorMessage = `API 錯誤: ${response.status}`;

      try {
        const errorData = await response.json();
        errorMessage += ` - ${errorData.error || '未知錯誤'}`;
      } catch (jsonError) {
        errorMessage += ' - 無法解析錯誤響應';
      }

      throw new Error(errorMessage);
    }

    // 嘗試解析 JSON 響應
    try {
      const responseData = await response.json();
      return responseData;
    } catch (jsonError) {
      console.warn(`警告: 無法解析來自 ${endpoint} 的 JSON 響應`);
      return {}; // 返回空對象而不是拋出錯誤
    }
  } catch (error) {
    console.error(`請求 ${endpoint} 時出錯:`, error.message);
    throw error;
  }
}

// 獲取所有門店
async function getAllStores() {
  try {
    console.log('獲取所有門店...');
    const response = await makeRequest('stores');

    // 檢查響應格式 - 處理不同的可能響應結構
    let stores = [];

    if (Array.isArray(response)) {
      // 如果響應直接是數組
      stores = response;
    } else if (response && typeof response === 'object') {
      // 如果響應是對象，嘗試獲取 stores 屬性
      if (Array.isArray(response.stores)) {
        stores = response.stores;
      } else if (response.data && Array.isArray(response.data)) {
        stores = response.data;
      }
    }

    console.log(`找到 ${stores.length} 家門店`);
    return stores;
  } catch (error) {
    console.error('獲取門店失敗:', error.message);
    return [];
  }
}

// 獲取所有網關
async function getGatewaysByStore(storeId) {
  try {
    console.log(`獲取門店 ${storeId} 的網關...`);
    const gateways = await makeRequest(`gateways?storeId=${storeId}`);
    console.log(`找到 ${gateways.length} 個網關`);
    return gateways;
  } catch (error) {
    console.error(`獲取門店 ${storeId} 的網關失敗:`, error.message);
    return [];
  }
}

// 創建新網關
async function createGateway(storeId) {
  console.log(`為門店 ${storeId} 創建新網關`);

  // 生成隨機網關名稱
  const gatewayName = `新網關-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`;

  // 創建網關資料
  const gatewayData = {
    name: gatewayName,
    macAddress: generateRandomMac(),
    status: 'online',
    model: 'GW-2000',
    wifiFirmwareVersion: '1.0.0',
    btFirmwareVersion: '2.0.0',
    ipAddress: generateRandomIp(),
    storeId: storeId,
    lastSeen: new Date()
  };

  try {
    // 創建新網關
    const gateway = await makeRequest('gateways', 'POST', gatewayData);
    console.log(`網關創建成功: ${gateway.name} (${gateway._id})`);
    return gateway;
  } catch (error) {
    console.error(`創建網關 ${gatewayData.name} 失敗:`, error.message);
    return null;
  }
}

// 生成隨機MAC地址
function generateRandomMac() {
  const hexDigits = "0123456789ABCDEF";
  let mac = "";
  for (let i = 0; i < 6; i++) {
    let part = "";
    for (let j = 0; j < 2; j++) {
      part += hexDigits.charAt(Math.floor(Math.random() * 16));
    }
    mac += part;
    if (i < 5) mac += ":";
  }
  return mac;
}

// 生成隨機IP地址
function generateRandomIp() {
  return `192.168.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}`;
}

// 生成 JWT token (舊版本，保留作為備用)
const generateToken = (gatewayId, storeId, macAddress) => {
  const payload = {
    gatewayId: gatewayId,
    storeId: storeId,
    macAddress: macAddress, // 添加 MAC 地址到 token 中
    type: 'gateway'
  };

  return jwt.sign(payload, jwtSecret, { expiresIn: '30d' });
};

// 建立 WebSocket 連接
async function connectWebSocket(gateway, storeId, maxChunkSize = 200, maxSingleMessageSize = 2048, preferredFormat = 'rawdata') {
  // 清空自訂設備列表
  customDevices = [];

  // 獲取網關ID
  const gatewayId = gateway._id;

  // 獲取網關MAC地址
  const macAddress = gateway.macAddress || generateRandomMac();

  // 詢問用戶選擇 token 生成方式
  console.log('\n🔑 Token 生成方式選擇:');
  console.log('1. API 方式 - 使用服務器 API 獲取 token（推薦）');
  console.log('2. 本地方式 - 使用本地生成 token（測試用）');
  console.log('3. 自動方式 - 先嘗試 API，失敗時回退到本地（預設）');

  const tokenChoice = await prompt('請選擇 token 生成方式 (1-3，預設為3): ') || '3';

  let token;
  let url;
  let tokenMethod = '';

  switch (tokenChoice) {
    case '1':
      // 僅使用 API 方式
      console.log('🔑 使用 API 方式獲取 token...');
      const apiTokenData = await getGatewayToken(gatewayId, storeId, macAddress);

      if (apiTokenData && apiTokenData.token) {
        token = apiTokenData.token;
        url = apiTokenData.wsUrl;
        tokenMethod = 'API';
        console.log(`✅ 已從 API 獲取 Gateway token`);
      } else {
        console.error('❌ API 獲取 token 失敗，無法繼續');
        throw new Error('API token 獲取失敗');
      }
      break;

    case '2':
      // 僅使用本地方式
      console.log('🔑 使用本地方式生成 token...');
      token = generateToken(gatewayId, storeId, macAddress);
      const wsPath = `/ws/store/${storeId}/gateway/${gatewayId}`;
      const wsBaseUrl = buildWebSocketUrl(wsPath, 3001);
      url = `${wsBaseUrl}?token=${token}`;
      tokenMethod = '本地';
      console.log(`✅ 已使用本地方式生成 Gateway token`);
      break;

    case '3':
    default:
      // 自動方式（先 API 後本地）
      console.log('🔑 使用自動方式獲取 token（先嘗試 API）...');
      const autoTokenData = await getGatewayToken(gatewayId, storeId, macAddress);

      if (autoTokenData && autoTokenData.token) {
        // 使用 API 獲取的 token
        token = autoTokenData.token;
        url = autoTokenData.wsUrl;
        tokenMethod = 'API（自動）';
        console.log(`✅ 已從 API 獲取 Gateway token`);
      } else {
        // 回退到本地方法
        console.log('⚠️ API 獲取 token 失敗，回退到本地方法');
        token = generateToken(gatewayId, storeId, macAddress);
        const wsPath = `/ws/store/${storeId}/gateway/${gatewayId}`;
        const wsBaseUrl = buildWebSocketUrl(wsPath, 3001);
        url = `${wsBaseUrl}?token=${token}`;
        tokenMethod = '本地（回退）';
        console.log(`✅ 已使用本地方式生成 Gateway token`);
      }
      break;
  }

  console.log(`\n📋 Token 生成摘要:`);
  console.log(`  - 生成方式: ${tokenMethod}`);
  console.log(`  - Gateway ID: ${gatewayId}`);
  console.log(`  - Store ID: ${storeId}`);
  console.log(`  - MAC Address: ${macAddress}`);
  console.log(`===============================`);

  console.log(`🔗 準備連接到 WebSocket 服務器: ${url}`);

  const ws = new WebSocket(url);
  let pingInterval, deviceStatusInterval;

  // 預先生成 5 台不同尺寸的隨機 MAC 設備，保持固定
  const randomDevices = [
    // 2.13" BWR (122x250)
    {
      macAddress: generateRandomMac(),
      model: 'EPD-2.13-BWR-RND',
      hardwareVersion: '*******',
      status: 'online',
      data: {
        size: '2.13"',
        battery: 0, // 初始化值，會在報告時更新
        rssi: 0,    // 初始化值，會在報告時更新
        imageCode: '12345213',
        colorType: 'BWR'
      }
    },
    // 2.9" BWR (128x296)
    {
      macAddress: generateRandomMac(),
      model: 'EPD-2.9-BWR-RND',
      hardwareVersion: '*******',
      status: 'online',
      data: {
        size: '2.9"',
        battery: 0,
        rssi: 0,
        imageCode: '12345290',
        colorType: 'BWR'
      }
    },
    // 3.7" BWRY (240x416)
    {
      macAddress: generateRandomMac(),
      model: 'EPD-3.7-BWRY-RND',
      hardwareVersion: '*******',
      status: 'online',
      data: {
        size: '3.7"',
        battery: 0,
        rssi: 0,
        imageCode: '12345370',
        colorType: 'BWRY'
      }
    },
    // 6" BW (1024x758)
    {
      macAddress: generateRandomMac(),
      model: 'EPD-6.0-BW-RND',
      hardwareVersion: '*******',
      status: 'online',
      data: {
        size: '6"',
        battery: 0,
        rssi: 0,
        imageCode: '12345600',
        colorType: 'BW'
      }
    },
    // 7.5" BWR (800x480)
    {
      macAddress: generateRandomMac(),
      model: 'EPD-7.5-BWR-RND',
      hardwareVersion: '*******',
      status: 'online',
      data: {
        size: '7.5"',
        battery: 0,
        rssi: 0,
        imageCode: '12345750',
        colorType: 'BWR'
      }
    }
  ];

  // 將隨機設備的 MAC 地址註冊到 deviceImageCodes 中
  randomDevices.forEach(device => {
    if (!deviceImageCodes[device.macAddress]) {
      deviceImageCodes[device.macAddress] = device.data.imageCode;
    }
  });

  // 清理定時器
  function clearIntervals() {
    if (pingInterval) clearInterval(pingInterval);
    if (deviceStatusInterval) clearInterval(deviceStatusInterval);
  }
  ws.on('open', () => {
    console.log('連接已建立');

    // 發送 ping 消息
    const pingMessage = {
      type: 'ping',
      timestamp: Date.now()
    };

    console.log('發送 ping 消息:', pingMessage);
    ws.send(JSON.stringify(pingMessage));

    // 定期發送心跳 (ping) 消息，確保連接保持活躍
    pingInterval = setInterval(() => {
      const pingMessage = {
        type: 'ping',
        timestamp: Date.now()
      };
      console.log('發送定期 ping 消息');
      ws.send(JSON.stringify(pingMessage));
    }, 25000);  // 設置為25秒，低於服務器端的30秒心跳檢查

    // 顯示命令幫助
    function showHelp() {
      console.log('\n可用命令:');
      console.log('  help - 顯示此幫助信息');
      console.log('  q - 退出程序');
      console.log('  add - 添加自定義設備');
      console.log('  list - 列出所有當前模擬的設備');
      console.log('  remove <序號> - 移除指定序號的自定義設備');
    }

    // 添加自定義設備
    async function addCustomDevice() {
      try {
        const mac = await prompt('請輸入設備 MAC 地址 (格式如 AA:BB:CC:DD:EE:FF): ');
        if (!mac.match(/^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$/)) {
          console.log('錯誤: MAC 地址格式不正確，請使用格式 AA:BB:CC:DD:EE:FF');
          return;
        }

        const model = await prompt('請輸入設備型號 (例如 EPD-2.9-BWR，默認 EPD-CUSTOM): ') || 'EPD-CUSTOM';
        const hardwareVersion = await prompt('請輸入硬體版本 (x.x.x.x格式，默認 *******): ') || '*******';
        const status = (await prompt('請輸入設備狀態 (online/offline，默認 online): ')) || 'online';
        const size = await prompt('請輸入設備尺寸 (例如 2.9", 4.2"，默認 2.9"): ') || '2.9"';
        const imageCode = await prompt('請輸入設備 imageCode (可選，用於模擬設備回報): ') || '';
        const colorType = await prompt('請輸入設備顏色類型 (BW/BWR/BWRY，默認 BW): ') || 'BW';

        // 如果用戶提供了 imageCode，存儲到本地映射中
        if (imageCode) {
          deviceImageCodes[mac] = imageCode;
          console.log(`已將 imageCode ${imageCode} 存儲到設備 ${mac} 的本地映射中`);
        }

        // 創建自定義設備（不包含 dataId 和 imageCode，符合 server 架構要求）
        const customDevice = {
          macAddress: mac,
          model: model,
          hardwareVersion: hardwareVersion,
          status: status,
          // 注意：不包含 dataId，因為這是由前端或API控制的欄位，不是由裝置自己回報的欄位
          data: {
            size: size,
            battery: Math.floor(Math.random() * 100),
            rssi: -1 * Math.floor(Math.random() * 100),
            // 注意：不包含 imageCode，因為裝置回報不應該包含此字段
            colorType: colorType.toUpperCase() // 確保使用大寫的簡短代碼
          }
        };

        // 添加到自定義設備列表
        customDevices.push(customDevice);
        console.log(`成功添加自定義設備: MAC=${mac}, 型號=${model}, 硬體版本=${hardwareVersion}, 尺寸=${size}`);
        console.log(`目前共有 ${customDevices.length} 個自定義設備`);
      } catch (err) {
        console.error('添加設備時出錯:', err.message);
      }
    }

    // 列出所有設備
    function listDevices() {
      console.log('\n設備列表:');
      console.log('1. 預設設備 - MAC: 00:11:22:33:44:55, 狀態: online');
      console.log('   型號: EPD-2.9-BWR, 硬體版本: *******, 尺寸: 2.9"(128x296), 本地 imageCode: 12345678, colorType: BWR');
      console.log('2. 預設設備 - MAC: 00:11:22:33:44:66, 狀態: online');
      console.log('   型號: EPD-2.9-BWRY, 硬體版本: *******, 尺寸: 2.9"(128x296), 本地 imageCode: 12345678, colorType: BWRY');
      console.log('3. 預設設備 - MAC: 00:11:22:33:44:77, 狀態: online');
      console.log('   型號: EPD-2.9-TEST, 硬體版本: *******, 尺寸: 2.9"(128x296), 本地 imageCode: 12345678, colorType: XYZ123 (不存在的顏色類型)');
      console.log('4. 預設設備 - MAC: 00:11:22:33:44:36, 狀態: online');
      console.log('   型號: EPD-2.9-BW, 硬體版本: *******, 尺寸: 2.9"(128x296), 本地 imageCode: 12345628, colorType: BW');
      console.log('5. 預設設備 - MAC: 00:11:22:33:44:13, 狀態: online');
      console.log('   型號: EPD-2.13-BWR, 硬體版本: *******, 尺寸: 2.13"(122x250), 本地 imageCode: 12345213, colorType: BWR');
      console.log('6. 預設設備 - MAC: 00:11:22:33:44:37, 狀態: online');
      console.log('   型號: EPD-3.7-BWRY, 硬體版本: *******, 尺寸: 3.7"(240x416), 本地 imageCode: 12345370, colorType: BWRY');
      console.log('7. 預設設備 - MAC: 00:11:22:33:44:60, 狀態: online');
      console.log('   型號: EPD-6.0-BW, 硬體版本: *******, 尺寸: 6"(1024x758), 本地 imageCode: 12345600, colorType: BW');
      console.log('8. 預設設備 - MAC: 00:11:22:33:44:75, 狀態: online');
      console.log('   型號: EPD-7.5-BWR, 硬體版本: *******, 尺寸: 7.5"(800x480), 本地 imageCode: 12345750, colorType: BWR');

      if (customDevices.length === 0) {
        console.log('尚未添加任何自定義設備');
      } else {
        console.log('自定義設備:');
        customDevices.forEach((device, index) => {
          console.log(`${index + 9}. MAC: ${device.macAddress}, 狀態: ${device.status}`);
          console.log(`   型號: ${device.model || 'N/A'}, 硬體版本: ${device.hardwareVersion || 'N/A'}, 尺寸: ${device.data?.size || 'N/A'}, 本地 imageCode: ${deviceImageCodes[device.macAddress] || 'N/A'}, colorType: ${device.data?.colorType || 'N/A'}`);
        });
      }
    }

    // 移除自定義設備
    function removeDevice(index) {
      const deviceIndex = parseInt(index) - 9; // 調整索引（因為有八個預設設備，索引從9開始）
      if (isNaN(deviceIndex) || deviceIndex < 0 || deviceIndex >= customDevices.length) {
        console.log('錯誤: 無效的設備序號');
        listDevices(); // 顯示正確的設備列表供參考
        return;
      }

      const removedDevice = customDevices.splice(deviceIndex, 1)[0];
      console.log(`已移除設備: MAC=${removedDevice.macAddress}`);
    }

    // 顯示初始幫助信息
    console.log('\n連接成功！您可以輸入命令來管理模擬設備。');    // 顯示命令幫助（添加新命令）
    function showHelp() {
      console.log('\n可用命令:');
      console.log('  help - 顯示此幫助信息');
      console.log('  q - 退出程序');
      console.log('  add - 添加自定義設備');
      console.log('  list - 列出所有當前模擬的設備');
      console.log('  remove <序號> - 移除指定序號的自定義設備');
      console.log('  request-image <裝置MAC> - 請求特定裝置的預覽圖像');
    }

    showHelp();

    // 請求設備圖片預覽
    function requestDeviceImage(macAddress) {
      if (!macAddress || !macAddress.match(/^([0-9A-Fa-f]{2}[:]){5}([0-9A-Fa-f]{2})$/)) {
        console.log('錯誤: MAC 地址格式不正確，請使用格式 AA:BB:CC:DD:EE:FF');
        return;
      }

      // 發送請求圖片預覽的消息
      const requestImageMessage = {
        type: 'requestPreviewImage',
        macAddress: macAddress,
        timestamp: Date.now()
      };

      console.log(`發送圖片預覽請求，裝置 MAC: ${macAddress}`);
      ws.send(JSON.stringify(requestImageMessage));
    }

    // 監聽用戶輸入
    rl.on('line', async (input) => {
      const command = input.trim();

      if (command === 'q') {
        console.log('正在關閉連接並退出...');
        clearIntervals();
        ws.close();
        setTimeout(() => process.exit(0), 1000);
      } else if (command === 'help') {
        showHelp();
      } else if (command === 'add') {
        await addCustomDevice();
      } else if (command === 'list') {
        listDevices();
      } else if (command.startsWith('remove ')) {
        const index = command.split(' ')[1];
        removeDevice(index);
      } else if (command.startsWith('request-image ')) {
        const macAddress = command.split(' ')[1];
        requestDeviceImage(macAddress);
      } else if (command) {
        console.log('未知命令。輸入 help 顯示可用命令。');
      }
    });    // 每 5 秒發送一次設備狀態消息
    deviceStatusInterval = setInterval(() => {
      // 預設設備1 - 2.9" BWR
      const defaultDevice1 = {
        macAddress: '00:11:22:33:44:55',
        model: 'EPD-2.9-BWR',
        hardwareVersion: '*******',
        status: 'online',
        // 注意：不包含 dataId，因為這是由前端或API控制的欄位，不是由裝置自己回報的欄位
        data: {
          size: '2.9"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          // 檢查是否有本地儲存的 imageCode，如果有則使用它
          imageCode: deviceImageCodes['00:11:22:33:44:55'] || '12345678',
          colorType: 'BWR'        // 預設顏色類型 (簡短代碼)
        }
      };

      // 預設設備2 - 2.9" BWRY
      const defaultDevice2 = {
        macAddress: '00:11:22:33:44:66',
        model: 'EPD-2.9-BWRY',
        hardwareVersion: '*******',
        status: 'online',
        // 注意：不包含 dataId，因為這是由前端或API控制的欄位，不是由裝置自己回報的欄位
        data: {
          size: '2.9"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          // 檢查是否有本地儲存的 imageCode，如果有則使用它
          imageCode: deviceImageCodes['00:11:22:33:44:66'] || '12345678',
          colorType: 'BWRY'      // 預設顏色類型 (簡短代碼)
        }
      };

      // 預設設備3 - 2.9" 不存在的顏色類型
      const defaultDevice3 = {
        macAddress: '00:11:22:33:44:77',
        model: 'EPD-2.9-TEST',
        hardwareVersion: '*******',
        status: 'online',
        // 注意：不包含 dataId，因為這是由前端或API控制的欄位，不是由裝置自己回報的欄位
        data: {
          size: '2.9"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          // 檢查是否有本地儲存的 imageCode，如果有則使用它
          imageCode: deviceImageCodes['00:11:22:33:44:77'] || '12345678',
          colorType: 'XYZ123'    // 不存在的顏色類型
        }
      };

      // 預設設備4 - 2.9" BW
      const defaultDevice4 = {
        macAddress: '00:11:22:33:44:36',
        model: 'EPD-2.9-BW',
        hardwareVersion: '*******',
        status: 'online',
        // 注意：不包含 dataId，因為這是由前端或API控制的欄位，不是由裝置自己回報的欄位
        data: {
          size: '2.9"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          // 檢查是否有本地儲存的 imageCode，如果有則使用它
          imageCode: deviceImageCodes['00:11:22:33:44:36'] || '12345628',
          colorType: 'BW'      // 預設顏色類型 (簡短代碼)
        }
      };

      // 新增設備5 - 2.13" BWR
      const defaultDevice5 = {
        macAddress: '00:11:22:33:44:13',
        model: 'EPD-2.13-BWR',
        hardwareVersion: '*******',
        status: 'online',
        data: {
          size: '2.13"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          imageCode: deviceImageCodes['00:11:22:33:44:13'] || '12345213',
          colorType: 'BWR'
        }
      };

      // 新增設備6 - 3.7" BWRY
      const defaultDevice6 = {
        macAddress: '00:11:22:33:44:37',
        model: 'EPD-3.7-BWRY',
        hardwareVersion: '*******',
        status: 'online',
        data: {
          size: '3.7"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          imageCode: deviceImageCodes['00:11:22:33:44:37'] || '12345370',
          colorType: 'BWRY'
        }
      };

      // 新增設備7 - 6" BW
      const defaultDevice7 = {
        macAddress: '00:11:22:33:44:60',
        model: 'EPD-6.0-BW',
        hardwareVersion: '*******',
        status: 'online',
        data: {
          size: '6"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          imageCode: deviceImageCodes['00:11:22:33:44:60'] || '12345600',
          colorType: 'BW'
        }
      };

      // 新增設備8 - 7.5" BWR
      const defaultDevice8 = {
        macAddress: '00:11:22:33:44:75',
        model: 'EPD-7.5-BWR',
        hardwareVersion: '*******',
        status: 'online',
        data: {
          size: '7.5"',
          battery: Math.floor(Math.random() * 100),
          rssi: -1 * Math.floor(Math.random() * 100),
          imageCode: deviceImageCodes['00:11:22:33:44:75'] || '12345750',
          colorType: 'BWR'
        }
      };      // 更新隨機設備的動態資料（電池電量和信號強度）
      const updatedRandomDevices = randomDevices.map(device => {
        return {
          ...device,
          data: {
            ...device.data,
            battery: Math.floor(Math.random() * 100),
            rssi: -1 * Math.floor(Math.random() * 100),
            // 使用已儲存的 imageCode 或原始值
            imageCode: deviceImageCodes[device.macAddress] || device.data.imageCode
          }
        };
      });

      // 合併預設設備和自定義設備
      const updatedCustomDevices = customDevices.map(device => {
        if (deviceImageCodes[device.macAddress]) {
          return {
            ...device,
            data: {
              ...device.data,
              imageCode: deviceImageCodes[device.macAddress]
            }
          };
        }
        return device;
      });

      const allDevices = [defaultDevice1, defaultDevice2, defaultDevice3, defaultDevice4, defaultDevice5, defaultDevice6, defaultDevice7, defaultDevice8, ...updatedRandomDevices, ...updatedCustomDevices];

      const deviceStatusMessage = {
        type: 'deviceStatus',
        devices: allDevices
      };

      console.log(`\n===== 發送設備狀態消息 (共 ${allDevices.length} 個設備) =====`);
      console.log('詳細資料:');
      console.log(JSON.stringify(deviceStatusMessage, null, 2));

      // 顯示每個設備的詳細信息
      console.log('\n設備列表:');
      allDevices.forEach((device, index) => {
        console.log(`  ${index + 1}. MAC: ${device.macAddress}, 狀態: ${device.status}`);
        console.log(`     型號: ${device.model || 'N/A'}, 硬體版本: ${device.hardwareVersion || 'N/A'}`);
        console.log(`     尺寸: ${device.data?.size || 'N/A'}, 電池: ${device.data?.battery || 'N/A'}, 訊號: ${device.data?.rssi || 'N/A'}`);
        console.log(`     imageCode: ${device.data?.imageCode || 'N/A'}, colorType: ${device.data?.colorType || 'N/A'}`);
      });
      console.log('=======================================');

      ws.send(JSON.stringify(deviceStatusMessage));
    }, 5000);

    // 注意：網關信息消息現在在收到 welcome 消息時立即發送，
    // 不再需要30秒延遲，以確保服務器能及時獲取網關的分片能力
  });

  // 分片接收狀態管理
  let chunkReceiver = null;

  ws.on('message', async (data) => {
    try {
      // 改進的消息類型檢測 - 支援大量小分片
      let isJsonMessage = false;

      if (typeof data === 'string') {
        isJsonMessage = true;
      } else if (data instanceof Buffer) {
        // 更嚴格的 JSON 檢測邏輯
        if (data.length > 0 && data[0] === 0x7B) { // 以 '{' 開頭
          // 進一步檢查是否真的是 JSON
          if (data.length > 1 && data[data.length - 1] === 0x7D) { // 以 '}' 結尾
            isJsonMessage = true;
          } else if (data.length < 500) { // 小數據包，嘗試解析
            try {
              const str = data.toString('utf8');
              if (str.trim().startsWith('{') && str.trim().endsWith('}')) {
                JSON.parse(str); // 嘗試解析以確認是有效 JSON
                isJsonMessage = true;
              }
            } catch (e) {
              // 解析失敗，不是 JSON
              isJsonMessage = false;
            }
          }
        }
        // 對於分片數據，如果有活躍的分片接收器且數據長度符合預期，直接當作二進制處理
        else if (chunkReceiver && data.length >= 4) {
          // 這很可能是嵌入式 Index 的分片數據
          isJsonMessage = false;
        }
      }

      if (isJsonMessage) {
        // JSON 訊息處理
        const message = JSON.parse(data.toString());
        console.log('收到JSON消息:', message);

        // 特別處理歡迎消息
        if (message.type === 'welcome') {
          console.log('收到歡迎消息，連接成功建立');

          // 立即發送網關信息消息，讓服務器知道網關的分片能力
          const gatewayInfoMessage = {
            type: 'gatewayInfo',
            info: {
              macAddress: gateway.macAddress || 'AA:BB:CC:DD:EE:FF',
              model: gateway.model || 'Gateway Model 002',
              hardwareVersion: gateway.hardwareVersion || '*******',
              wifiFirmwareVersion: gateway.wifiFirmwareVersion || '1.0.0',
              btFirmwareVersion: gateway.btFirmwareVersion || '2.0.0',
              ipAddress: gateway.ipAddress || '*************',

              // 新增：分片傳輸能力支援
              chunkingSupport: {
                enabled: true,                        // 是否支援分片傳輸
                maxChunkSize: maxChunkSize,          // 每個分片的最大大小
                maxSingleMessageSize: maxSingleMessageSize,  // 單次 JSON 訊息的最大大小限制
                embeddedIndex: true,                 // 是否支援嵌入式 Index 模式
                jsonHeader: true,                    // 是否支援 JSON Header 模式（向後兼容）
                supportedFormat: preferredFormat     // 偏好的 rawdata 格式
              }
            }
          };

          console.log('🚀 立即發送網關信息消息（包含分片能力）:', gatewayInfoMessage);
          ws.send(JSON.stringify(gatewayInfoMessage));
        }

        // 特別處理確認消息
        if (message.type === 'pong') {
          console.log('收到服務器 pong 回應');
        }

        // 處理分片傳輸相關訊息
        if (message.type === 'image_chunk_start') {
          console.log('🚀 開始接收分片傳輸:', message);
          chunkReceiver = new ChunkReceiver(ws);
          await chunkReceiver.handleChunkStart(message);
          return;
        }

        if (message.type === 'image_chunk_complete') {
          console.log('🏁 分片傳輸完成:', message);
          if (chunkReceiver) {
            await chunkReceiver.handleChunkComplete(message);
            chunkReceiver = null;
          }
          return;
        }

        // 儲存 imageCode (無論是否有圖像數據)
        if (message.deviceMac && message.imageCode) {
          console.log(`收到裝置 ${message.deviceMac} 的 imageCode: ${message.imageCode}`);
          deviceImageCodes[message.deviceMac] = message.imageCode;
          console.log(`已將 imageCode 儲存至本地變數，將在下次發送裝置狀態時使用`);
        }

        // 明確檢查 message 對象中是否包含 imageData 欄位
        if (message.hasOwnProperty('imageData')) {
          console.log('檢測到消息中包含 imageData 欄位，準備處理圖像數據...');

          // 檢查 imageData 格式
          if (typeof message.imageData === 'string') {
            if (message.imageData.length < 10) {
              console.warn('收到的圖像數據太短，可能不是有效圖像');
              return;
            }

            if (message.imageData.startsWith('data:image')) {
              // 處理 data URL 格式的 base64 編碼圖片數據
              console.log('處理 data URL 格式的 base64 圖像數據');
              await saveBase64Image(message.imageData, message.deviceMac || 'unknown', message.imageCode);
            } else {
              // 處理純 base64 字符串（不帶前綴）
              console.log('處理純 base64 編碼圖像數據');
              try {
                // 檢查是否是有效的 base64 字符串
                if (!isValidBase64(message.imageData.replace(/\s/g, ''))) {
                  console.warn('收到的不是有效的 base64 字符串，跳過處理');
                  return;
                }

                const imageBuffer = Buffer.from(message.imageData, 'base64');

                // 檢查是否為有效圖片
                if (!isValidImageBuffer(imageBuffer)) {
                  console.warn('解碼後的數據不是有效的圖片格式，跳過保存');
                  console.log(`數據前幾個字節: ${imageBuffer.slice(0, 16).toString('hex')}`);
                  return;
                }

                await saveImageFile(imageBuffer, message.deviceMac || 'unknown', null, message.imageCode);
              } catch (err) {
                console.error('無法處理收到的 base64 圖像數據:', err.message);
              }
            }
          } else {
            console.error('收到的 imageData 不是有效的字符串格式');
          }
        }

        // 檢查 message 對象中是否包含 rawdata 欄位
        if (message.hasOwnProperty('rawdata')) {
          console.log('檢測到消息中包含 rawdata 欄位，準備處理原始數據...');

          try {
            const dataType = message.dataType || 'rawdata';
            await saveRawData(message.rawdata, message.deviceMac || 'unknown', message.imageCode, dataType);
          } catch (err) {
            console.error('處理 rawdata 時出錯:', err.message);
          }
        }

        // 對於其他類型的消息，僅記錄類型但不處理圖像
        if (!message.hasOwnProperty('imageData') && !message.hasOwnProperty('rawdata')) {
          if (message.type === 'preview') {
            console.log('收到預覽消息，但不包含 imageData 或 rawdata 欄位，跳過數據處理');
          }
        }
      } else {
        // 二進制數據處理（嵌入式Index分片）
        if (chunkReceiver) {
          await chunkReceiver.handleBinaryChunkData(data);
          return;
        } else {
          console.warn('收到二進制數據但沒有活躍的分片接收器');
          return;
        }
      }
    } catch (error) {
      // 改進的錯誤處理 - 支援大量小分片場景
      const isLikelyChunkData = chunkReceiver && data instanceof Buffer && data.length >= 4;

      if (isLikelyChunkData) {
        // 如果有活躍的分片接收器且數據看起來像分片，直接處理
        console.log(`嘗試將數據作為分片處理 (長度: ${data.length} bytes)`);
        try {
          await chunkReceiver.handleBinaryChunkData(data);
          return; // 成功處理，直接返回
        } catch (chunkError) {
          console.error('作為分片數據處理失敗:', chunkError.message);
        }
      }

      // 只有在非分片數據或分片處理失敗時才記錄詳細錯誤
      console.error('解析收到的消息時出錯:', error.message);

      // 簡化調試信息，避免在大量分片時產生過多日誌
      if (data instanceof Buffer) {
        console.error(`數據類型: Buffer, 長度: ${data.length} bytes`);

        // 只對小數據包顯示詳細信息
        if (data.length <= 100) {
          console.error(`前16字節 (hex): ${data.subarray(0, Math.min(16, data.length)).toString('hex')}`);
          console.error(`完整數據 (string): ${data.toString('utf8')}`);
        } else {
          console.error(`前16字節 (hex): ${data.subarray(0, 16).toString('hex')}`);
        }
      } else {
        console.error(`數據類型: ${typeof data}, 內容: ${data}`);
      }
    }
  });
  // 檢查 base64 字符串是否有效
  function isValidBase64(base64Str) {
    // base64 字符串應該符合特定的格式 (只包含 A-Z, a-z, 0-9, +, /, =)
    // 並且長度應該是 4 的倍數 (可能帶有填充字符 =)
    const base64Regex = /^[A-Za-z0-9+/]+={0,2}$/;
    return base64Regex.test(base64Str) && base64Str.length % 4 === 0;
  }  // 保存 Base64 編碼的圖像
  async function saveBase64Image(base64Data, deviceMac, imageCode = null) {
    try {
      console.log(`準備處理 imageData 欄位的數據並保存為圖片，裝置 MAC: ${deviceMac}`);

      // 如果有 imageCode，先保存到本地變數中
      if (imageCode) {
        deviceImageCodes[deviceMac] = imageCode;
        console.log(`已將裝置 ${deviceMac} 的 imageCode ${imageCode} 儲存至本地變數`);
      }

      // 從 Data URL 中提取 base64 數據部分
      const matches = base64Data.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);

      if (!matches || matches.length !== 3) {
        console.log('未匹配到標準 data URL 格式，嘗試作為純 base64 處理...');

        // 檢查是否是有效的 base64 字符串
        if (!isValidBase64(base64Data.replace(/\s/g, ''))) {
          console.warn('無效的 base64 字符串，跳過保存');
          return;
        }

        // 嘗試直接作為 base64 字符串處理
        const imageBuffer = Buffer.from(base64Data, 'base64');

        // 檢查解碼後的數據是否是有效的圖片
        if (!isValidImageBuffer(imageBuffer)) {
          console.warn('base64 解碼後不是有效的圖片數據，跳過保存');
          return;
        }

        await saveImageFile(imageBuffer, deviceMac, 'png', imageCode);
        return;
      }

      // 提取 MIME 類型和 base64 數據
      const mimeType = matches[1];
      const base64Buffer = matches[2];
      const extension = mimeType.split('/')[1] || 'png';

      // 檢查提取出的 base64 字符串是否有效
      if (!isValidBase64(base64Buffer.replace(/\s/g, ''))) {
        console.warn('從 Data URL 提取的 base64 字符串無效，跳過保存');
        return;
      }

      // 創建圖像數據的 buffer
      const imageBuffer = Buffer.from(base64Buffer, 'base64');

      // 檢查解碼後的數據是否有效
      if (!isValidImageBuffer(imageBuffer)) {
        console.warn('從 Data URL 解碼的數據不是有效的圖片，跳過保存');
        return;
      }      // 保存圖像文件
      await saveImageFile(imageBuffer, deviceMac, extension, imageCode);
    } catch (err) {
      console.error('保存 base64 圖像時出錯:', err.message);
    }
  }
  // 檢查圖片數據是否有效
  function isValidImageBuffer(buffer) {
    // 檢查 buffer 是否為有效 Buffer 並且長度大於最小有效圖片大小
    // 通常有效的圖片至少應該有幾百字節
    if (!Buffer.isBuffer(buffer) || buffer.length < 100) {
      return false;
    }

    // 檢查常見圖片格式的文件頭標識 (Magic Numbers)
    const isPNG = buffer.length >= 8 &&
                  buffer[0] === 0x89 &&
                  buffer[1] === 0x50 && // P
                  buffer[2] === 0x4E && // N
                  buffer[3] === 0x47 && // G
                  buffer[4] === 0x0D &&
                  buffer[5] === 0x0A &&
                  buffer[6] === 0x1A &&
                  buffer[7] === 0x0A;

    const isJPEG = buffer.length >= 3 &&
                   buffer[0] === 0xFF &&
                   buffer[1] === 0xD8 &&
                   buffer[2] === 0xFF;

    const isGIF = buffer.length >= 6 &&
                  buffer[0] === 0x47 && // G
                  buffer[1] === 0x49 && // I
                  buffer[2] === 0x46 && // F
                  buffer[3] === 0x38 && // 8
                  (buffer[4] === 0x39 || buffer[4] === 0x37) && // 9 or 7
                  buffer[5] === 0x61;   // a

    const isBMP = buffer.length >= 2 &&
                  buffer[0] === 0x42 && // B
                  buffer[1] === 0x4D;   // M

    // 如果是常見圖片格式之一，則認為有效
    return isPNG || isJPEG || isGIF || isBMP;
  }  // 保存圖像文件
  async function saveImageFile(imageBuffer, deviceMac = 'unknown', extension = 'png', imageCode = null) {
    try {
      console.log(`準備保存圖片文件，裝置 MAC: ${deviceMac}，文件格式: ${extension}`);

      // 檢查 buffer 是否有效
      if (!Buffer.isBuffer(imageBuffer) || imageBuffer.length === 0) {
        throw new Error('無效的圖像數據 buffer');
      }

      // 驗證這是一個有效的圖片
      if (!isValidImageBuffer(imageBuffer)) {
        console.warn(`收到無效的圖像數據，長度: ${imageBuffer.length} 字節，不進行保存`);
        return;
      }

      // 建立保存圖像的目錄
      const saveDir = path.join(__dirname, 'saved_images');
      if (!fs.existsSync(saveDir)) {
        fs.mkdirSync(saveDir, { recursive: true });
      }

      // 創建文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `preview_${deviceMac.replace(/:/g, '')}_${timestamp}.${extension}`;
      const filePath = path.join(saveDir, fileName);

      // 寫入文件
      fs.writeFileSync(filePath, imageBuffer);

      console.log(`已成功將圖像保存到: ${filePath}`);

      // 如果提供了 imageCode，直接使用它
      if (imageCode) {
        console.log(`使用接收到的 imageCode: ${imageCode}`);

        // 保存到裝置 imageCode 集合中
        deviceImageCodes[deviceMac] = imageCode;
        console.log(`已將 imageCode ${imageCode} 儲存至本地變數，將在下次發送裝置狀態時使用`);
      }
    } catch (err) {
      console.error('保存圖像文件時出錯:', err.message);
    }
  }

  // 保存原始數據為 bin 檔案
  async function saveRawData(rawdata, deviceMac = 'unknown', imageCode = null, format = 'rawdata') {
    try {
      console.log(`準備保存 ${format} 格式的數據為 bin 檔案，裝置 MAC: ${deviceMac}`);

      let rawBuffer;

      // 處理不同格式的 rawdata
      if (typeof rawdata === 'string') {
        // 如果是 base64 字符串，先解碼
        if (isValidBase64(rawdata.replace(/\s/g, ''))) {
          console.log('處理 base64 編碼的原始數據');
          rawBuffer = Buffer.from(rawdata, 'base64');
        } else {
          console.warn('rawdata 不是有效的 base64 字符串');
          return;
        }
      } else if (Buffer.isBuffer(rawdata)) {
        // 如果已經是 Buffer，直接使用
        rawBuffer = rawdata;
      } else if (Array.isArray(rawdata)) {
        // 如果是數組，轉換為 Buffer
        rawBuffer = Buffer.from(rawdata);
      } else {
        console.error('不支援的 rawdata 格式:', typeof rawdata);
        return;
      }

      // 檢查 buffer 是否有效
      if (!Buffer.isBuffer(rawBuffer) || rawBuffer.length === 0) {
        console.warn('無效的原始數據 buffer，跳過保存');
        return;
      }

      // 建立保存原始數據的目錄（與圖像保存在同一位置）
      const saveDir = path.join(__dirname, 'saved_images');
      if (!fs.existsSync(saveDir)) {
        fs.mkdirSync(saveDir, { recursive: true });
      }

      // 根據格式調整檔案名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `${format}_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
      const filePath = path.join(saveDir, fileName);

      // 寫入文件
      fs.writeFileSync(filePath, rawBuffer);

      console.log(`已成功將 ${format} 數據保存到: ${filePath}`);
      console.log(`數據大小: ${rawBuffer.length} 字節`);

      // 如果是壓縮格式，嘗試解壓縮驗證
      if (format !== 'rawdata') {
        try {
          const decompressed = decompressRawdata(rawBuffer, format);
          console.log(`解壓縮驗證成功，解壓縮後大小: ${decompressed.length} 字節`);

          if (rawBuffer.length < decompressed.length) {
            const compressionRatio = (rawBuffer.length / decompressed.length * 100).toFixed(1);
            console.log(`壓縮比: ${compressionRatio}%`);
          }

          // 保存解壓縮後的數據
          const decompressedFileName = `rawdata_decompressed_${deviceMac.replace(/:/g, '')}_${timestamp}.bin`;
          const decompressedFilePath = path.join(saveDir, decompressedFileName);
          fs.writeFileSync(decompressedFilePath, Buffer.from(decompressed));
          console.log(`解壓縮數據已保存到: ${decompressedFilePath}`);
        } catch (decompressError) {
          console.error('解壓縮驗證失敗:', decompressError.message);
        }
      }

      // 如果提供了 imageCode，記錄關聯
      if (imageCode) {
        console.log(`關聯的 imageCode: ${imageCode}`);

        // 保存到裝置 imageCode 集合中
        deviceImageCodes[deviceMac] = imageCode;
        console.log(`已將 imageCode ${imageCode} 儲存至本地變數，將在下次發送裝置狀態時使用`);
      }

      // 顯示原始數據的前幾個字節（用於調試）
      const previewBytes = rawBuffer.subarray(0, Math.min(16, rawBuffer.length));
      console.log(`數據前 ${previewBytes.length} 字節 (hex): ${Buffer.from(previewBytes).toString('hex')}`);

    } catch (err) {
      console.error('保存原始數據時出錯:', err.message);
    }
  }

  // 增強: 處理WebSocket原生ping事件
  ws.on('ping', (data) => {
    console.log('收到服務器ping檢測');
    // WebSocket 客戶端會自動回應 pong，這裡只需記錄
  });

  ws.on('error', (error) => {
    console.error('WebSocket 錯誤:', error);
    clearIntervals();
  });

  ws.on('close', (code, reason) => {
    console.log(`連接已關閉，代碼: ${code}，原因: ${reason}`);
    clearIntervals();

  // 詢問用戶是否要重連
    promptReconnect(gateway, storeId);
  });

  // 處理程序結束時，關閉 WebSocket 連接
  process.on('SIGINT', () => {
    console.log('關閉 WebSocket 連接...');
    clearIntervals();
    ws.close();
    rl.close();
    process.exit();
  });

  return ws;
}

// 提示用戶是否重新連接
async function promptReconnect(gateway, storeId) {
  const answer = await prompt('連接已斷開，是否嘗試重新連接? (y/n): ');
  if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
    console.log('嘗試重新連接...');
    connectWebSocket(gateway, storeId);
  } else {
    console.log('不再嘗試重新連接。程序將退出。');
    rl.close();
    process.exit();
  }
}

// 自訂設備列表的參考
let customDevices = [];

// 主函數
async function main() {
  try {
    console.log('EPD 網關模擬器（互動版）');

    // 登入
    const username = await prompt('請輸入用戶名 (默認: root): ') || 'root';
    const password = await prompt('請輸入密碼 (默認: 123456789): ') || '123456789';

    const loginSuccess = await login(username, password);
    if (!loginSuccess) {
      throw new Error('登入失敗，無法繼續執行');
    }

    // 獲取所有門店
    const stores = await getAllStores();
    if (stores.length === 0) {
      throw new Error('找不到任何門店，請先運行 generate-test-data.js');
    }

    // 顯示門店列表
    console.log('\n可用門店列表:');
    stores.forEach((store, index) => {
      console.log(`${index + 1}. ${store.name} (ID: ${store.id})`);
    });

    // 讓用戶選擇門店
    const storeIndex = parseInt(await prompt('\n請選擇門店 (輸入數字): ')) - 1;
    if (isNaN(storeIndex) || storeIndex < 0 || storeIndex >= stores.length) {
      throw new Error('無效的門店選擇');
    }

    const selectedStore = stores[storeIndex];
    console.log(`已選擇門店: ${selectedStore.name} (ID: ${selectedStore.id})`);    // 獲取選定門店的網關
    const gateways = await getGatewaysByStore(selectedStore.id);
    // 即使沒有網關也允許繼續，因為用戶可以創建新網關
    if (gateways.length === 0) {
      console.log(`\n注意: 門店 ${selectedStore.name} 目前沒有網關，但您可以選擇創建新網關。`);
    }    // 顯示網關列表
    console.log('\n可用選項:');
    console.log('0. 創建新網關');

    if (gateways.length > 0) {
      console.log('\n現有網關列表:');
      gateways.forEach((gateway, index) => {
        console.log(`${index + 1}. ${gateway.name} (ID: ${gateway._id})`);
        console.log(`   MAC: ${gateway.macAddress}`);
        console.log(`   IP: ${gateway.ipAddress || 'N/A'}`);
        console.log(`   型號: ${gateway.model || 'N/A'}`);
        console.log(`   狀態: ${gateway.status || 'unknown'}`);
        console.log('   -----------------------');
      });
    } else {
      console.log('\n此門店目前沒有現有網關，請選擇選項 0 創建新網關。');
    }    // 讓用戶選擇網關或創建新網關
    const gatewayChoice = await prompt('\n請選擇要模擬的網關或創建新網關 (輸入數字): ');
    const gatewayIndex = parseInt(gatewayChoice);

    let selectedGateway;

    // 檢查用戶是否選擇創建新網關
    if (gatewayIndex === 0) {
      // 創建新網關
      selectedGateway = await createGateway(selectedStore.id);
      if (!selectedGateway) {
        throw new Error('創建網關失敗');
      }
      console.log(`已成功創建新網關: ${selectedGateway.name}`);
    } else {
      // 選擇現有網關
      const index = gatewayIndex - 1;

      // 檢查選擇是否有效
      if (isNaN(index) || index < 0 || index >= gateways.length) {
        // 如果沒有現有網關，或者選擇無效，提示用戶重新選擇或創建新網關
        if (gateways.length === 0) {
          console.log('此門店沒有現有網關，將自動為您創建新網關');
          selectedGateway = await createGateway(selectedStore.id);
          if (!selectedGateway) {
            throw new Error('創建網關失敗');
          }
          console.log(`已成功創建新網關: ${selectedGateway.name}`);
        } else {
          throw new Error('無效的網關選擇');
        }
      } else {
        selectedGateway = gateways[index];
      }
    }

    console.log(`已選擇網關: ${selectedGateway.name} (ID: ${selectedGateway._id})`);

    // 詢問分片傳輸參數設定
    console.log('\n===== 分片傳輸參數設定 =====');
    console.log('請設定要模擬的分片傳輸能力：');

    // 選擇 maxChunkSize
    console.log('\n1. maxChunkSize (每個分片的最大大小):');
    console.log('1. 20 bytes (極小分片，測試用)');
    console.log('2. 200 bytes (小型設備)');
    console.log('3. 1024 bytes (中型設備)');
    console.log('4. 4096 bytes (大型設備)');
    console.log('5. 自定義大小');

    const chunkSizeChoice = await prompt('請選擇 maxChunkSize 選項 (1-5，預設為2): ') || '2';
    let maxChunkSize = 200; // 預設值

    switch (chunkSizeChoice) {
      case '1':
        maxChunkSize = 20;
        break;
      case '2':
        maxChunkSize = 200;
        break;
      case '3':
        maxChunkSize = 1024;
        break;
      case '4':
        maxChunkSize = 4096;
        break;
      case '5':
        const customChunkSize = await prompt('請輸入自定義 maxChunkSize (bytes): ');
        maxChunkSize = parseInt(customChunkSize) || 200;
        break;
      default:
        maxChunkSize = 200;
    }

    // 選擇 maxSingleMessageSize
    console.log('\n2. maxSingleMessageSize (單次 JSON 訊息的最大大小):');
    console.log('1. 500 bytes (小值，用於測試 JSON 訊息大小檢查)');
    console.log('2. 1024 bytes (中等值)');
    console.log('3. 2048 bytes (標準值，預設)');
    console.log('4. 4096 bytes (大值)');
    console.log('5. 自定義大小');

    const messageSizeChoice = await prompt('請選擇 maxSingleMessageSize 選項 (1-5，預設為3): ') || '3';
    let maxSingleMessageSize = 2048; // 預設值

    switch (messageSizeChoice) {
      case '1':
        maxSingleMessageSize = 500;
        break;
      case '2':
        maxSingleMessageSize = 1024;
        break;
      case '3':
        maxSingleMessageSize = 2048;
        break;
      case '4':
        maxSingleMessageSize = 4096;
        break;
      case '5':
        const customMessageSize = await prompt('請輸入自定義 maxSingleMessageSize (bytes): ');
        maxSingleMessageSize = parseInt(customMessageSize) || 2048;
        break;
      default:
        maxSingleMessageSize = 2048;
    }

    // 選擇偏好的 rawdata 格式
    console.log('\n3. 偏好的 rawdata 格式:');
    console.log('1. rawdata (未壓縮格式，預設)');
    console.log('2. runlendata (Run-Length 壓縮格式)');

    const formatChoice = await prompt('請選擇格式偏好選項 (1-2，預設為1): ') || '1';
    let preferredFormat = 'rawdata';

    switch (formatChoice) {
      case '1':
        preferredFormat = 'rawdata';
        break;
      case '2':
        preferredFormat = 'runlendata';
        break;
      default:
        preferredFormat = 'rawdata';
    }

    console.log(`\n已設定分片參數:`);
    console.log(`  - maxChunkSize: ${maxChunkSize} bytes`);
    console.log(`  - maxSingleMessageSize: ${maxSingleMessageSize} bytes`);
    console.log(`  - 偏好格式: ${preferredFormat}`);
    console.log('===============================\n');

    // 連接到 WebSocket 服務器
    console.log('正在連接到 WebSocket 服務器...');
    await connectWebSocket(selectedGateway, selectedStore.id, maxChunkSize, maxSingleMessageSize, preferredFormat);

    console.log('\n使用說明:');
    console.log('1. 此測試客戶端會自動嘗試連接到WebSocket服務器');
    console.log('2. Token 生成方式選擇:');
    console.log('   - API 方式: 使用服務器 API 獲取 token（推薦）');
    console.log('   - 本地方式: 使用本地生成 token（測試用）');
    console.log('   - 自動方式: 先嘗試 API，失敗時回退到本地（預設）');
    console.log('3. 會自動發送 ping、設備狀態和網關信息');
    console.log('4. 分片傳輸功能:');
    console.log(`   - maxChunkSize: ${maxChunkSize} bytes`);
    console.log(`   - maxSingleMessageSize: ${maxSingleMessageSize} bytes`);
    console.log(`   - 偏好格式: ${preferredFormat}`);
    console.log('   - 支援兩階段分片決策邏輯');
    console.log('5. 使用以下命令管理模擬設備:');
    console.log('   - help: 顯示命令幫助');
    console.log('   - add: 新增自定義設備');
    console.log('   - list: 列出所有模擬設備');
    console.log('   - remove <序號>: 移除指定序號的自定義設備');
    console.log('   - request-image <裝置MAC>: 請求特定裝置的預覽圖像');
    console.log('   - q: 退出程序');
    console.log('6. 如果連接斷開，系統會詢問您是否重連');
    console.log('7. 按 Ctrl+C 也可以終止程序');
    console.log('8. 收到的數據會自動保存到 saved_images 目錄:');
    console.log('   - 圖片數據 (imageData) 保存為 .png/.jpg 等圖片格式');
    console.log('   - 原始數據 (rawdata) 保存為 .bin 二進制檔案');
    console.log('   - 分片重組數據保存為 rawdata_chunked_*.bin 檔案');
    console.log('9. 分片決策邏輯測試:');
    console.log('   - 第一階段: rawdata 大小 vs maxChunkSize');
    console.log('   - 第二階段: JSON 訊息大小 vs maxSingleMessageSize');
    console.log('   - 可通過調整參數測試不同的分片觸發條件\n');

  } catch (error) {
    console.error('錯誤:', error.message);
    rl.close();
    process.exit(1);
  }
}

// 啟動程序
main().catch(error => {
  console.error('未處理的錯誤:', error);
  rl.close();
  process.exit(1);
});
