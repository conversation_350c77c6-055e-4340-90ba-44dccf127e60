import { DisplayColorType } from '../types';

// Device interface as specified in the requirements
export interface Device {
  _id?: string;        // MongoDB ID
  macAddress: string;  // MAC地址
  status: 'online' | 'offline';  // 在線狀態
  model?: string;      // 型號
  hardwareVersion?: string; // 硬體版本 (x.x.x.x格式)
  dataId: string;      // 關聯的數據ID
  dataBindings?: string | Record<string, any>; // 關聯的多個數據ID映射關係 (JSON字符串或對象)
  templateId?: string; // 關聯的模板ID
  previewImage?: string; // 設備預覽圖 (base64)
  storeId: string;     // 所屬門店ID
  imageUpdateStatus?: string; // 圖片更新狀態 ('已更新' | '未更新')

  // 設備初始化與網關關係
  initialized?: boolean; // 是否已初始化
  primaryGatewayId?: string; // 主要網關ID
  otherGateways?: string[];  // 其他發現此設備的網關ID列表
  gatewaySelectionMode?: 'auto' | 'manual'; // 網關選擇模式：自動或手動

  // 用戶綁定相關
  userId?: string;     // 綁定的用戶ID

  lastSeen: Date | null;      // 最後在線時間
  createdAt: Date | null;     // 創建時間
  updatedAt: Date | null;     // 更新時間
  note?: string;       // 備註
  code?: string;       // 編號

  // 設備數據 - 移至data對象中
  data?: {
    size?: string;     // 尺寸
    rssi?: number;     // RSSI值
    battery?: number;  // 電量百分比
    imageCode?: string; // 圖片編碼 (用於與 WebSocket 發送圖片的 imageCode 比對，僅由服務器更新)
    colorType?: DisplayColorType; // 顯示顏色類型 (用於過濾比對)
    [key: string]: any; // 允許其他自定義數據
  }
}

// Device status type
export type DeviceStatus = 'online' | 'offline';

// Device filter options
export interface DeviceFilter {
  status?: DeviceStatus;
  size?: string;
  search?: string;
}
